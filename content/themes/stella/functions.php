<?php
//include the autoloader
use Helpers\CPT;
use Helpers\CPT_Handler;
use Helpers\Taxonomy;

require_once get_template_directory() . '/includes/autoloader.php';
$development = (defined('WP_LOCAL_DEV') && WP_LOCAL_DEV) || strpos($_SERVER['HTTP_HOST'] . $_SERVER['PHP_SELF'], '.dev') !== false ? true : false;
//---------------------------------------------------------------------------------
//  Development Mode On / Off
//---------------------------------------------------------------------------------
// if ($development) {
//     add_action('houston_after_footer', function () {

//         $file = 'http://localhost:35729/livereload.js';
//         $file_headers = @get_headers($file);
//         if ($file_headers) {
//             wp_enqueue_script('livereload', 'http://localhost:35729/livereload.js', '', '', true);
//         }
//     });
// }

//---------------------------------------------------------------------------------
//	Require Files and Load up Stella (Load with hook to be able to use with child theme)
//  Moved to Raket\Stella class.
//---------------------------------------------------------------------------------
new Stella\Setup();
new Raket\TinyMce();
//new Raket\Admin_Mode();
new Raket\Cookie_Bar();

// dump and exit function
if (!function_exists('dd')) {
    function dd(...$args)
    {
        dump(...$args);
        exit;
    }
}

// dump
if (!function_exists('dump')) {
function dump(...$args)
    {
        foreach ($args as $dump) {
            echo '<pre class="dd">' . print_r($dump, true) . '</pre>';
        }
    }
}


// Update datepicker fields on cpt 'events' save.
// Needed to sort by date/time not only date. 

function my_acf_save_post($post_id)
{
    // only affect cpt 'events'
    $post_type = get_post_type($post_id);
    if ($post_type != 'events') {
        return;
    }

    // Check the new value of a specific field.
    $start_date = get_field('event_start_date', $post_id);
    $start_time = get_field('event_start_time', $post_id);
    $end_date = get_field('event_end_date', $post_id);
    $end_time = get_field('event_end_time', $post_id);


    $format = 'Y-m-d H:i:s';
    $start_date_time = DateTime::createFromFormat($format, $start_date . ' ' . $start_time);
    $end_date_time = DateTime::createFromFormat($format, $end_date . ' ' . $end_time);
    $start = $start_date_time->format('Y-m-d H:i:s');
    $end = $end_date_time->format('Y-m-d H:i:s');

    update_field('event_start', $start, $post_id);
    update_field('event_end', $end, $post_id);
};
add_action('acf/save_post', 'my_acf_save_post');

// Validate start/end times of event

function event_acf_validate_value($valid, $value, $field, $input)
{
    // bail early if value is already invalid
    if (!$valid) {
        return $valid;
    }

    $start_date = $_POST['acf']['field_61164c4590068'];
    $start_time = $_POST['acf']['field_611b5cc0baaca'];
    $end_date = $_POST['acf']['field_61164c9e90069'];
    $end_time = $_POST['acf']['field_611b5cf6baacb'];

    $start_date_time = strtotime($start_date . ' ' . $start_time);
    $end_date_time = strtotime($end_date . ' ' . $end_time);

     if ($start_date_time > $end_date_time) {
            return "Starttid kan inte vara senare än sluttid.";
      }

    return $valid;
}
add_filter('acf/validate_value/key=field_61164c4590068', 'event_acf_validate_value', 10, 4);

// Change format of Date-string
function convertDate($date)
{
    $myDateTime = DateTime::createFromFormat('Y-m-d', $date);
    $newDateString = $myDateTime->format('j F');
    setlocale(LC_TIME, array('sv_SE.UTF-8', 'sv_SE@euro', 'sv_SE', 'swedish'));
    $newDateString = strftime('%e %B', $myDateTime->getTimeStamp());
    return $newDateString;
}

// Change format of Date-string (year)
function convertYear($date)
{
    $myDateTime = DateTime::createFromFormat('Y-m-d', $date);
    setlocale(LC_TIME, array('sv_SE.UTF-8', 'sv_SE@euro', 'sv_SE', 'swedish'));
    $newDateString = strftime('%Y', $myDateTime->getTimeStamp());
    return $newDateString;
}

// Change format of Time-string
function convertTime($time)
{
    $myTime = DateTime::createFromFormat('H:i:s', $time);
    $newTimeString = $myTime->format('H:i');
    return $newTimeString;
}

// Add Editor style
add_editor_style('editor-style.css');
// Add Gutenberg
require_once(get_template_directory() . '/gutenberg/setup.php');
// Add stella functions
require_once(get_template_directory() . '/includes/class/Stella/_functions.php');


//---------------------------------------------------------------------------------
//	Register post types and taxonomies (Use the Helpers\CPT_Handler class)
//  Just uncomment the lines below and define your CPT:s and taxonomies
//---------------------------------------------------------------------------------

$handler = new CPT_Handler("stella");




// Lokaler
$handler->add_cpt(new CPT("objects", [
    "s_name" => "Lokal",
    "name" => "Lokaler",
], [
    'public' => true,
    'label'  => 'Lokaler',
    'has_archive' => true,
    'menu_icon' => 'dashicons-admin-network',
    'rewrite' => array('slug' => 'lokaler'),
    'show_in_rest' => true,
    'supports' => array('editor', 'title'),
]));

$handler->add_taxonomy(new Taxonomy("object_type", "objects", [
    "s_name" => "Typ",
    "name" => "Typer",
    'show_in_rest' => true,
    'show_ui'           => true,
    'show_admin_column' => true,
]));

$handler->add_taxonomy(new Taxonomy("object_area", "objects", [
    "s_name" => "Område",
    "name" => "Områden",
    'show_in_rest' => true,
    'show_ui'           => true,
    'show_admin_column' => true,
]));


// Events
$handler->add_cpt(new CPT("events", [
    "s_name" => "Event",
    "name" => "Events"
], [
    'public' => true,
    'label' => 'Event',
    'has_archive' => true,
    'menu_icon' => 'dashicons-calendar-alt',
    'rewrite' => array('slug' => 'event'),
    'supports' => array('editor', 'title', 'thumbnail'),
    'show_in_rest' => true,
]));

$handler->add_taxonomy(new Taxonomy(
    "event_category",
    "events",
    [
        "s_name" => "Kategori",
        "name" => "Kategorier",
        'show_ui'           => true,
        'show_admin_column' => true,
    ],
    [
        'show_in_rest' => true,
    ]


));


// Staff
$handler->add_cpt(new CPT("people", [
    "s_name" => "Kontaktperson",
    "name" => "Kontaktpersoner"
], ['public' => true]));

// Karta
$handler->add_cpt(new CPT("places", [
    "s_name" => "kartplats",
    "name" => "Karta"
], [
    'public' => true,
    'label' => 'Karta',
    'has_archive' => false,
    'menu_icon' => 'dashicons-admin-site',
    'rewrite' => array('slug' => 'platser'),
    'show_in_rest' => true,
    'supports' => array('title'),
]));


// Resturanger
$handler->add_cpt(new CPT("restaurants", [
    "s_name" => "Restaurang",
    "name" => "Restauranger",
], [
    'public' => true,
    'label'  => 'Restauranger',
    'has_archive' => true,
    'menu_icon' => 'dashicons-food',
    'rewrite' => array('slug' => 'restauranger'),
    'show_in_rest' => true,
    'supports' => array('title'),
]));


// Add new user role without capabilities
add_role('restaurant', __('Restaurang'), array(
    'read' => false,
    'edit_posts'   => false,
    'delete_posts' => false,
));

// Options page - Load after init to prevent textdomain issues
function setup_acf_options_pages() {
    if (function_exists('acf_add_options_page')) {
        acf_add_options_page(array(
            'page_title'     => 'Temainställningar',
            'menu_title'    => 'Temainställningar',
            'menu_slug'     => 'theme-settings',
            'capability'    => 'edit_posts',
            'redirect'        => true
        ));

        acf_add_options_sub_page(array(
            'page_title'     => 'Sidfot',
            'menu_title'    => 'Sidfot',
            'parent_slug'    => 'theme-settings',
        ));
    }
}
add_action('init', 'setup_acf_options_pages');

//---------------------------------------------------------------------------------
//	Custom login logo path
//  ** Please use a relative path from theme **
//  ** add_theme_support('custom-login-logo') must me set **
//---------------------------------------------------------------------------------
function stella_logo_path_filter($path)
{
    return '/assets/src/img/logo.png';
}
//add_filter( 'stella_login_logo_path', 'stella_logo_path_filter', 10, 1 );



//---------------------------------------------------------------------------------
//	Nav walker
//---------------------------------------------------------------------------------
class Wrapper_Walker_Nav_Menu extends Walker_Nav_Menu
{

    /**
     * Start lvl
     */
    function start_lvl(&$output, $depth = 0, $args = array())
    {
        $indent = ($depth > 0  ? str_repeat("\t", $depth) : '');
        $display_depth = ($depth + 1);
        $classes = array(
            'sub-menu',
            'menu-depth-' . $display_depth
        );
        $class_names = implode(' ', $classes);
        if ($display_depth == 1) {

            $output .= "\n" . $indent . '<div class="sub-menu-wrap"><div class="container"><div class="-offset-menu"></div><ul class="sub-menu ' . $class_names . '">' . "\n";
        } else {
            $output .= "\n" . $indent . '<ul class="child-menu">' . "\n";
        }
    }

    /**
     * End el
     */
    public function end_el(&$output, $item, $depth = 0, $args = null)
    {
        if (isset($args->item_spacing) && 'discard' === $args->item_spacing) {
            $t = '';
            $n = '';
        } else {
            $t = "\t";
            $n = "\n";
        }
        /**
         * Do shortcut magic here
         */
        $shortcuts = get_field('menu_shortcuts', $item);
        if (!empty($shortcuts)) {
            ob_start();
            load_module('parts/general/offset-menu.php', [
                'shortcuts' => $shortcuts
            ]);
            $offset_menu = ob_get_contents();
            ob_end_clean();
            $output = str_replace('<div class="-offset-menu"></div>', "<div class=\"offset-menu\"><div class=\"offset-menu__inner\"><span class=\"offset-menu__title\">Mer innehåll</span> $offset_menu</div></div>", $output);
        }
        $output .= "</li>{$n}";
    }
}

/**
 * Hide shortcuts on not 0 depth in menu
 */
// add_action('admin_head', function () {
// 	echo '<style>
//     [data-name="menu_shortcuts"] {
// 		display:none;
// 	}
// 	.menu-item-depth-0 > div > div > [data-name="menu_shortcuts"] {
// 		display:block;
// 	}
//   </style>';
// });


function custom_wp_nav_menu_objects($items, $args)
{

    foreach ($items as $item) {
        $children = get_field('menu_children', $item);

        if ($children) {
            $item->classes = ['has-child-menu'];
        }
    }
    return $items;
}

/*
function my_wp_nav_menu_objects($items, $args)
{

	foreach ($items as &$item) {

		$color = get_field('menu_color', $item);
		$description = get_field('menu_description', $item);
		$icon = get_field('menu_icon', $item);
		$test = get_field('second_menu', $item);

		if ($color) {
			array_push($item->classes, 'menu-item-' . $color);
		}

		if (!empty($description)) {
			$item->title .= '<span class="menu-description">' . $description . '</span>';
		}

		if (!empty($icon)) {
			$icon = '<span class="fal fa-' . $icon . ' menu-icon"></span>';
			$item->title = $icon . $item->title;
		}
	}
	return $items;
}
add_filter('wp_nav_menu_objects', 'my_wp_nav_menu_objects', 10, 2);
}*/


//---------------------------------------------------------------------------------
//	Responsive embeds
//---------------------------------------------------------------------------------
add_action('after_setup_theme', function () {
    add_theme_support('responsive-embeds');
});


//---------------------------------------------------------------------------------
//  Admin bar in footer
//---------------------------------------------------------------------------------
function fb_move_admin_bar()
{
    if (is_user_logged_in()) {
        echo '
        <style type="text/css">
            body {
            margin-top: -32px;
            padding-bottom: 32px;
            }
            body.admin-bar #wphead {
               padding-top: 0;
            }
            #wpadminbar {
                top: auto !important;
                bottom: 0;
            }
            #wpadminbar .quicklinks .menupop .ab-sub-wrapper {
                bottom: 32px;
            }
        @media screen and (max-width: 782px) {
            body {
                margin-top: -46px;
                padding-bottom: 46px;
            }
        }
        @media screen and (max-width: 600px) {
            #wpadminbar {
                position: fixed;
            }
        }
        </style>';
    }
}
add_action('wp_head', 'fb_move_admin_bar', 100);



//---------------------------------------------------------------------------------
//	Color palette editor
//---------------------------------------------------------------------------------
add_theme_support('editor-color-palette', array(
    array(
        'name'  => __('Svart', 'genesis-sample'),
        'slug'  => 'black',
        'color' => '#000000',
    ),
    array(
        'name'  => __('Grå', 'genesis-sample'),
        'slug'  => 'gray',
        'color' => '#4B4B4B',
    ),
    array(
        'name'  => __('Blå', 'genesis-sample'),
        'slug'  => 'blue',
        'color'    => '#3839FB',
    ),
    array(
        'name'  => __('Röd', 'genesis-sample'),
        'slug'  => 'red',
        'color' => '#FA4D3D',
    )
));

add_action('enqueue_block_editor_assets', function () {
    wp_enqueue_script('awp-gutenberg-filters', get_template_directory_uri() . 'gutenberg/assets/js/GutenbergFilters.js', ['wp-edit-post']);
});

//---------------------------------------------------------------------------------
//	Force non-editors to frontend
//--------------------------------------------------------------------------------- *  
function my_login_redirect($redirect_to, $request, $user)
{
    //is there a user to check?	
    if (isset($user->roles) && is_array($user->roles)) {
        //check for admins
        if (in_array('restaurant', $user->roles)) {
            $pages = get_pages(array(
                'meta_key' => '_wp_page_template',
                'meta_value' => 'tpl-create.php'
            ));
            if (isset($pages[0])) {
                $redirect_to = get_page_link($pages[0]->ID);
            }
            return $redirect_to;
        } else {
            return $redirect_to;
        }
    } else {
        return $redirect_to;
    }
}

add_filter('login_redirect', 'my_login_redirect', 10, 3);

// Filter stuff
function add_query_vars_filter($vars)
{
    $vars[] = "program";
    return $vars;
}
add_filter('query_vars', 'add_query_vars_filter');

function sort_terms_hierarchicaly(array &$cats, array &$into, $parent = null)
{
    foreach ($cats as $i => $cat) {
        $into[$cat->term_id] = $cat;
        unset($cats[$i]);
    }

    foreach ($into as $topCat) {
        $topCat->children = array();
        sort_terms_hierarchicaly($cats, $topCat->children, $topCat);
    }
}

//---------------------------------------------------------------------------------
//	Get URL of page with template (for Events)
//--------------------------------------------------------------------------------- *  
function getTplPageURL($TEMPLATE_NAME)
{
    $url = null;
    $pages = get_pages(array(
        'meta_key' => '_wp_page_template',
        'meta_value' => $TEMPLATE_NAME
    ));
    if (isset($pages[0])) {
        $url = get_page_link($pages[0]->ID);
    }
    return $url;
}

add_action('save_post', 'save_post_callback');
function save_post_callback($post_id)
{
    global $post;
    if ($post->post_type != 'restaurants') {
        return;
    }
    return do_action('litespeed_purge_posttype', 'restaurants');
}



// function _validate_save_post()
// {
//     echo ("Is this thing running?");
//     // bail early if no $_POST
//     $acf = false;
//     foreach ($_POST as $key => $value) {
//         if (strpos($key, 'acf') === 0) {
//             if (!empty($_POST[$key])) {
//                 acf_validate_values($_POST[$key], $key);
//             }
//         }
//     }
// }
// add_action('acf/validate_save_post', '_validate_save_post', 5);


//log function
if (!function_exists('write_log')) {
    function write_log($log)
    {
        if (true === WP_DEBUG) {
            if (is_array($log) || is_object($log)) {
                error_log(print_r($log, true));
            } else {
                error_log($log);
            }
        }
    }
}

/** 
 * Remove full size from srcset (used in some templates)
 */
function remove_sizes_from_srcset($sources, $size_array, $image_src, $image_meta, $attachment_id) {
    foreach ($sources as $width => $source) {
        if (strpos($source['url'], wp_get_attachment_url($attachment_id)) === 0) {
            unset($sources[$width]);
        }
    }
    return $sources;
}

/**
 * Add a CRON job that will add custom thumbnail folders for automatic processing to Shortpixel
 */
add_action('init', function() {
    if (!wp_next_scheduled('shortpixel_add_custom_thumbnail_folders')) {
        wp_schedule_event(time(), 'daily', 'shortpixel_add_custom_thumbnail_folders');
    }
});

/**
 * Add custom thumbnail folders for automatic processing to Shortpixel
 */
add_action('shortpixel_add_custom_thumbnail_folders', function() {
    $folders = array();
    $uploads = wp_upload_dir();
    $dir = $uploads['basedir'] . '/';
    $year_dirs = scandir($dir);
    // Only keep those that start with 20
    $year_dirs = array_filter($year_dirs, function($year_dir) {
        return strpos($year_dir, '20') === 0;
    });
    foreach ($year_dirs as $year_dir) {
        if (is_dir($dir . $year_dir)) {
            $month_dirs = scandir($dir . $year_dir);
            // Only keep numeric ones (to exclude ., ..)
            $month_dirs = array_filter($month_dirs, function($month_dir) {
                return is_numeric($month_dir);
            });
            foreach ($month_dirs as $month_dir) {
                if (is_dir($dir . $year_dir . '/' . $month_dir) && is_dir($dir . $year_dir . '/' . $month_dir . '/thumbnails')) {
                    $folders[] = $dir . $year_dir . '/' . $month_dir . '/thumbnails';
                }
            }
        }
    }

    // Add folders to Shortpixel database table (wp_shortpixel_folders)
    if (!empty($folders)) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'shortpixel_folders';
        foreach ($folders as $folder) {
            // Check if folder already exists
            $existing_folder = $wpdb->get_var($wpdb->prepare("SELECT id FROM $table_name WHERE path = %s", $folder));
            if (empty($existing_folder)) {
                // Add folder
                $wpdb->insert($table_name, array(
                    'path' => $folder,
                    'name' => 'thumbnails',
                    'path_md5' => null,
                    'file_count' => null,
                    'status' => 0,
                    'parent' => 0,
                    'ts_checked' => null,
                    'ts_updated' => null,
                    'ts_created' => date("Y-m-d H:i:s", time()),
                ));
            }
        }
    }
});

/**
 * Unpublish 'events' posts where 'event_end' is older than 6 months
 */
add_action('init', function () {
    if (!wp_next_scheduled('unpublish_old_events_hook')) {
        wp_schedule_event(time(), 'daily', 'unpublish_old_events_hook');
    }
});

function unpublish_old_events() {
    $period = (str_contains(get_site_url(), 'innovatumdistrict.se')) ? strtotime('-6 months') : strtotime('-12 months');

    $query = new WP_Query([
        'post_type'      => 'events',
        'post_status'    => 'publish',
        'meta_key'       => 'event_end',
        'meta_value'     => date('Y-m-d', $period),
        'meta_compare'   => '<',
        'meta_type'      => 'DATE',
        'posts_per_page' => -1,
        'fields'         => 'ids',
    ]);

    if ($query->have_posts()) {
        foreach ($query->posts as $post_id) {
            wp_update_post([
                'ID'          => $post_id,
                'post_status' => 'draft',
            ]);
        }
    }
}
add_action('unpublish_old_events_hook', 'unpublish_old_events');

/**
 * Add custom columns for 'events' post type
 */
add_filter('manage_events_posts_columns', function ($columns) {
    $new_columns = [];

    foreach ($columns as $key => $value) {
        $new_columns[$key] = $value;
        if ($key === 'title') {
            $new_columns['event_start_date'] = 'Start';
            $new_columns['event_end_date'] = 'Slut';
        }
    }

    return $new_columns;
});

add_action('manage_events_posts_custom_column', function ($column, $post_id) {
    if ($column === 'event_start_date' || $column === 'event_end_date') {
        $date = get_field($column, $post_id);
        if ($date) {
            echo date_i18n('j F Y', strtotime($date));
        } else {
            echo '—';
        }
    }
}, 10, 2);

add_filter('manage_edit-events_sortable_columns', function ($columns) {
    $columns['event_start_date'] = 'event_start_date';
    $columns['event_end_date'] = 'event_end_date';
    return $columns;
});

add_action('pre_get_posts', function ($query) {
    if (!is_admin() || !$query->is_main_query()) return;

    $orderby = $query->get('orderby');

    if (in_array($orderby, ['event_start_date', 'event_end_date'])) {
        $query->set('meta_key', $orderby);
        $query->set('orderby', 'meta_value');
        $query->set('meta_type', 'DATE');
    }
});

add_action('admin_head', function () {
    $screen = get_current_screen();
    if ($screen->post_type === 'events') {
        echo '<style>
            .column-event_start_date,
            .column-event_end_date {
                width: 160px;
            }
        </style>';
    }
});

/**
 * Add defer attribute to our theme's javascript.
 *
 * @param string    $tag      The <script> tag for the enqueued script.
 * @param string    $handle   The script's handle.
 * @return string             The modified <script> tag.
 */
function add_defer_attribute_to_script( $tag, $handle ) {
    // Add the defer attribute to this specific script handle.
    if ('app' === $handle) {
        $tag = str_replace(' src', ' defer="defer" src', $tag);
    }
    return $tag;
}
add_filter('script_loader_tag', 'add_defer_attribute_to_script', 10, 2);