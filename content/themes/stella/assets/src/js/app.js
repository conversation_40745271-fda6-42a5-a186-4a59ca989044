// jQuery only exports one function, so we don't need the { }.
// The jQuery and jquery keywords are defined as global variables in package.json.
// This means we will be using the global jquery object provided by wordpress instead of importing our own.
import $ from 'jquery'
import Modernizr from 'modernizr'
import FastClick from 'fastclick'
import 'babel-polyfill'
import {} from '../../../../../../node_modules/featherlight/src/featherlight.js'

// import MobileMenu from './MobileMenu'
import DesktopMenu from './DesktopMenu'
import Blob from './Blob'
import CookieBar from './CookiBar'
import navigation from './navigation'
import slider from './slider'
import spotVideo from './spot-video'
import selects from './selects'
import LazyLoad from 'vanilla-lazyload'

var isMobile = Modernizr.mq('(max-width: 768px)')

// NEW: An array to store all created blobs and their containers for resizing.
let allBlobs = []
let blobResizeTimeout

require('./react/MapApp.js')

// The imports are all done - write your code as usual!
$(window).on('load', () => {
  // new MobileMenu()
  new DesktopMenu()
  new CookieBar()

  initBlobs()

  let lazyLoadInstance = new LazyLoad({
    elements_selector: '.lazy',
  })

  $('body').addClass('loaded')
  FastClick.attach(document.body)
  navigation.init()

  $('.gform_wrapper').on('click', 'input[type=submit]', function () {
    _gaq.push(['_trackEvent', 'formular', 'skicka'])
  })

  //add active classes to search aside navigation
  $('body.search .sub-navigation__list .menu-item').on('click', function () {
    $('body.search .sub-navigation__list .menu-item').removeClass(
      'current_page_item'
    )
    $(this).addClass('current_page_item')
  })

  slider()
  selects()
  spotVideo.init()

  /* Single Lediga lokaler top actions */

  $('.single-actions__link.has-dropdown').on('click', function (e) {
    e.preventDefault()
    $(this).parent().toggleClass('active')
  })

  $('.single-actions__link.print').on('click', function (e) {
    e.preventDefault()
    window.print()
  })

  $('.single-actions__link.scroller').on('click', function (event) {
    var target = $($(this).attr('href'))
    console.log(target.length)
    if ((target.length = 0)) {
      target = $('.content').find('.spot.person').first()
    }

    if (target.length) {
      event.preventDefault()
      var x = target.offset().top
      $('html, body').animate(
        {
          scrollTop: x - 100,
        },
        500
      )
    }
  })

  $('.category_select').on('change', function (e) {
    let select_val = $(e.currentTarget).val()
    console.log(select_val)
    window.location = select_val
  })
  
  // NEW: Add a debounced resize listener to handle blob adjustments.
  $(window).on('resize', () => {
    clearTimeout(blobResizeTimeout)
    blobResizeTimeout = setTimeout(handleBlobResize, 250)
  })
})

$(document).ready(function () {
  $('body').on('click', '.modal--close', function () {
    var currentFeather = jQuery.featherlight.current()
    if (currentFeather) {
      currentFeather.close()
    }
  })

  $(document).on('click', '#gallery--close', function () {
    console.log('test')
    $('.featherlight').click()
  })
})

/**
 * NEW: This function loops through all stored blobs and updates their size.
 */
function handleBlobResize() {
  for (const item of allBlobs) {
    // Get the new dimensions from the container element
    const newWidth = item.element.clientWidth;
    const newHeight = item.element.clientHeight;

    // Set the blob's size instantly (no animation)
    item.blob.setSize(newWidth, newHeight, false);
  }
}

function initBlobs() {
  const els = document.querySelectorAll('[data-blob]')

  if (els.length) {
    for (let i = 0; i < els.length; i++) {
      const el = els[i]

      // CRITICAL FIX: Remove any duplicate SVGs first to prevent layout issues
      const allSvgs = el.querySelectorAll('svg');
      const prerenderedSvgs = el.querySelectorAll('.blob-svg-prerendered');

      // If we have more SVGs than prerendered ones, remove the extras
      if (allSvgs.length > prerenderedSvgs.length) {
        allSvgs.forEach(svg => {
          if (!svg.classList.contains('blob-svg-prerendered')) {
            svg.remove();
          }
        });
      }

      // Check for a pre-rendered SVG inside the container
      const prerenderedSvg = el.querySelector('.blob-svg-prerendered');

      // Get vars (preserving your original parsing logic)
      let pointsData = null
      if (el.dataset.points) {
        let val = el.dataset.points
        val = val.replaceAll('x', '"x"')
        val = val.replaceAll('y', '"y"')
        pointsData = JSON.parse(val)
      }

      const tension = el.dataset.tension ? el.dataset.tension : null
      const color = el.dataset.color ? el.dataset.color : null
      const designedWidth = el.dataset.designedWidth
        ? el.dataset.designedWidth
        : el.clientWidth
      const designedHeight = el.dataset.designedHeight
        ? el.dataset.designedHeight
        : el.clientHeight

      // Prepare a settings object for the Blob class
      const blobSettings = {
        pointsData: pointsData,
        tension: tension,
        startWidth: designedWidth,
        startHeight: designedHeight,
        color: color,
      };

      if (prerenderedSvg) {
        blobSettings.element = prerenderedSvg;
      } else {
        blobSettings.container = el;
      }

      const blob = new Blob(blobSettings);

      // Set size at start
      blob.setSize(el.clientWidth, el.clientHeight, false)

      // NEW: Store the blob instance and its element for later resizing.
      allBlobs.push({ blob: blob, element: el });
    }
  }
}